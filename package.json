{"name": "phantom", "version": "0.0.2", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@supabase/supabase-js": "^2.39.7", "@sveltejs/adapter-auto": "6.0.1", "@sveltejs/kit": "2.21.1", "@sveltejs/vite-plugin-svelte": "5.0.3", "@types/node": "^22.15.3", "svelte": "5.30.2", "svelte-adapter-bun": "^0.5.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "vite": "6.3.5"}, "type": "module", "dependencies": {"axios": "^1.10.0", "crypto": "^1.0.1", "elysia": "1.1.26", "firebase-admin": "^13.4.0", "querystring": "^0.2.1", "westwallet-api": "^1.0.7"}}