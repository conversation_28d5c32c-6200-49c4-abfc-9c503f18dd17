import { env } from '$env/dynamic/private';
import { supabase } from '$lib/server/supabase.js';
import { CustomWestWalletAPI } from '$lib/server/customWestWalletClient.js';

// WestWallet API configuration
const WESTWALLET_API_KEY = env.WESTWALLET_API_KEY || '';
const WESTWALLET_SECRET_KEY = env.WESTWALLET_SECRET_KEY || '';
const WESTWALLET_BASE_URL = env.WESTWALLET_BASE_URL || 'https://api.westwallet.io';

// Supported currencies for wallet generation
const SUPPORTED_CURRENCIES = ['USDTTRC', 'TRX', 'TON', 'XMR'];

/**
 * Create WestWallet API client instance
 * @returns {any} WestWallet API client
 */
function createWestWalletClient() {
  if (!WESTWALLET_API_KEY || !WESTWALLET_SECRET_KEY) {
    throw new Error('WestWallet API credentials not configured. Please set WESTWALLET_API_KEY and WESTWALLET_SECRET_KEY environment variables.');
  }

  // Log credential status (without exposing actual values)
  console.log('WestWallet API configuration:', {
    hasApiKey: !!WESTWALLET_API_KEY,
    apiKeyLength: WESTWALLET_API_KEY?.length || 0,
    hasSecretKey: !!WESTWALLET_SECRET_KEY,
    secretKeyLength: WESTWALLET_SECRET_KEY?.length || 0,
    baseUrl: WESTWALLET_BASE_URL
  });

  return new CustomWestWalletAPI(WESTWALLET_API_KEY, WESTWALLET_SECRET_KEY, WESTWALLET_BASE_URL);
}

/**
 * Test WestWallet API connection and credentials
 * @returns {Promise<boolean>} True if connection is successful
 */
export async function testWestWalletConnection() {
  try {
    const client = createWestWalletClient();
    console.log('Testing WestWallet API connection...');

    // Try to get wallet balances as a test
    const balances = await client.walletBalances();
    console.log('WestWallet API connection successful:', balances);
    return true;
  } catch (error) {
    console.error('WestWallet API connection failed:', error);
    return false;
  }
}

/**
 * Generate a wallet address for a specific currency
 * @param {string} currency - The currency to generate address for
 * @param {string} teamId - The team ID for labeling
 * @returns {Promise<{address: string, dest_tag: string|null, currency: string}>} Generated address data
 */
async function generateWalletAddress(currency, teamId) {
  const client = createWestWalletClient();

  try {
    const label = `team_${teamId}_${currency}`;
    const ipnUrl = ''; // Can be configured if needed for payment notifications

    console.log(`Attempting to generate ${currency} address with label: ${label}`);
    const addressData = await client.generateAddress(currency, ipnUrl, label);
    console.log(`Successfully generated ${currency} address:`, addressData);

    return {
      address: addressData.address,
      currency: currency
    };
  } catch (error) {
    console.error(`Detailed error generating ${currency} wallet address for team ${teamId}:`, {
      error: error,
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown',
      constructor: error instanceof Error ? error.constructor?.name : 'Unknown'
    });

    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to generate ${currency} wallet address: ${errorMessage}`);
  }
}

/**
 * Generate all required wallets for a team
 * @param {string} teamInternalId - The team's internal UUID
 * @param {string} teamId - The team's public ID
 * @returns {Promise<any[]>} Array of generated wallet data
 */
export async function generateTeamWallets(teamInternalId, teamId) {
  const wallets = [];
  const errors = [];

  console.log(`Generating wallets for team ${teamId} (${teamInternalId})`);

  for (const currency of SUPPORTED_CURRENCIES) {
    try {
      console.log(`Generating ${currency} wallet for team ${teamId}`);

      // Generate wallet address using WestWallet API
      const walletData = await generateWalletAddress(currency, teamId);

      // Prepare wallet record for database
      const wallet = {
        team_internal_id: teamInternalId,
        team_id: teamId,
        currency: currency,
        address: walletData.address,
        created_at: new Date().toISOString()
      };

      // Insert wallet into database
      const { data, error } = await supabase
        .from('wallets')
        .insert([wallet])
        .select()
        .single();

      if (error) {
        console.error(`Error saving ${currency} wallet to database:`, error);
        errors.push(`Failed to save ${currency} wallet: ${error.message}`);
      } else {
        console.log(`Successfully created ${currency} wallet for team ${teamId}: ${walletData.address}`);
        wallets.push(data);
      }

    } catch (error) {
      console.error(`Error creating ${currency} wallet for team ${teamId}:`, error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`Failed to create ${currency} wallet: ${errorMessage}`);
    }
  }
  
  if (errors.length > 0) {
    console.warn(`Some wallets failed to generate for team ${teamId}:`, errors);
    // Don't throw error if some wallets were created successfully
    // Just log the errors for debugging
  }
  
  console.log(`Generated ${wallets.length} wallets for team ${teamId}`);
  return wallets;
}

/**
 * Get all wallets for a team
 * @param {string} teamId - The team's public ID
 * @returns {Promise<any[]>} Array of wallet data
 */
export async function getTeamWallets(teamId) {
  const { data, error } = await supabase
    .from('wallets')
    .select('*')
    .eq('team_id', teamId)
    .order('currency');

  if (error) {
    console.error(`Error fetching wallets for team ${teamId}:`, error);
    throw new Error(`Failed to fetch wallets: ${error.message}`);
  }

  return data || [];
}

/**
 * Get wallet by currency for a team
 * @param {string} teamId - The team's public ID
 * @param {string} currency - The currency
 * @returns {Promise<any|null>} Wallet data or null if not found
 */
export async function getTeamWalletByCurrency(teamId, currency) {
  const { data, error } = await supabase
    .from('wallets')
    .select('*')
    .eq('team_id', teamId)
    .eq('currency', currency)
    .single();

  if (error && error.code !== 'PGRST116') { // PGRST116 is "not found" error
    console.error(`Error fetching ${currency} wallet for team ${teamId}:`, error);
    throw new Error(`Failed to fetch ${currency} wallet: ${error.message}`);
  }

  return data || null;
}
