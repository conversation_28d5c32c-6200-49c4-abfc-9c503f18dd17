// Simple test file for wallet service
// This is a basic test to verify the wallet service functionality

import { generateTeamWallets, getTeamWallets } from './walletService.js';

/**
 * Test function to verify wallet generation
 * Note: This requires valid WestWallet API credentials in environment variables
 */
async function testWalletGeneration() {
    console.log('Testing wallet generation...');
    
    const testTeamId = 'test-team-' + Date.now();
    const testTeamInternalId = 'test-internal-' + Date.now();
    
    try {
        // Test wallet generation
        console.log(`Generating wallets for test team: ${testTeamId}`);
        const wallets = await generateTeamWallets(testTeamInternalId, testTeamId);
        
        console.log(`Generated ${wallets.length} wallets:`);
        wallets.forEach(wallet => {
            console.log(`- ${wallet.currency}: ${wallet.address}`);
        });
        
        // Test wallet retrieval
        console.log('\nRetrieving wallets from database...');
        const retrievedWallets = await getTeamWallets(testTeamId);
        
        console.log(`Retrieved ${retrievedWallets.length} wallets:`);
        retrievedWallets.forEach(wallet => {
            console.log(`- ${wallet.currency}: ${wallet.address}`);
        });
        
        console.log('\n✅ Wallet service test completed successfully!');
        
    } catch (error) {
        console.error('❌ Wallet service test failed:', error);
        throw error;
    }
}

/**
 * Test function to verify wallet retrieval for non-existent team
 */
async function testWalletRetrievalEmpty() {
    console.log('\nTesting wallet retrieval for non-existent team...');
    
    try {
        const wallets = await getTeamWallets('non-existent-team');
        console.log(`Retrieved ${wallets.length} wallets for non-existent team (should be 0)`);
        
        if (wallets.length === 0) {
            console.log('✅ Empty wallet retrieval test passed!');
        } else {
            throw new Error('Expected 0 wallets for non-existent team');
        }
        
    } catch (error) {
        console.error('❌ Empty wallet retrieval test failed:', error);
        throw error;
    }
}

// Export test functions for manual testing
export { testWalletGeneration, testWalletRetrievalEmpty };

// If running this file directly (for manual testing)
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('Running wallet service tests...\n');
    
    try {
        await testWalletRetrievalEmpty();
        // Uncomment the line below to test actual wallet generation
        // Note: This requires valid WestWallet API credentials
        // await testWalletGeneration();
        
        console.log('\n🎉 All tests completed!');
    } catch (error) {
        console.error('\n💥 Tests failed:', error);
        process.exit(1);
    }
}
