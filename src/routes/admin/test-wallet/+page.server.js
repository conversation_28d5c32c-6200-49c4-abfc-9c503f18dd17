import { testWestWalletConnection } from '$lib/server/walletService.js';
import { CustomWestWalletAPI } from '$lib/server/customWestWalletClient.js';
import { env } from '$env/dynamic/private';

export const load = async () => {
  return {};
};

export const actions = {
  testConnection: async () => {
    try {
      const result = await testWestWalletConnection();
      return { 
        success: true, 
        message: result ? 'Connection successful' : 'Connection failed',
        result 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error.message 
      };
    }
  },

  testSingleWallet: async ({ request }) => {
    const formData = await request.formData();
    const currency = formData.get('currency') || 'BTC';
    
    try {
      const client = new CustomWestWalletAPI(
        env.WESTWALLET_API_KEY,
        env.WESTWALLET_SECRET_KEY,
        env.WESTWALLET_BASE_URL || 'https://api.westwallet.io'
      );
      
      console.log(`Testing ${currency} wallet generation...`);
      const result = await client.generateAddress(currency, '', `test_${Date.now()}`);
      
      return { 
        success: true, 
        message: `${currency} wallet generated successfully`,
        result 
      };
    } catch (error) {
      console.error(`Error testing ${currency} wallet:`, error);
      return { 
        success: false, 
        error: error.message,
        currency 
      };
    }
  }
};
