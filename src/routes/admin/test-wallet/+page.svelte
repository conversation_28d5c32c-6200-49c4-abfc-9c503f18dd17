<script>
  import { enhance } from '$app/forms';
  
  export let data;
  export let form;
  
  let selectedCurrency = 'BTC';
  let loading = false;
  
  const currencies = ['BTC', 'ETH', 'USDTTRC', 'TRON', 'TON', 'XMR'];
  
  const handleEnhance = () => {
    loading = true;
    return async ({ result, update }) => {
      loading = false;
      await update();
    };
  };
</script>

<div class="admin-container">
  <div class="admin-card">
    <h1 class="admin-title">WestWallet API Test</h1>
    
    <div class="test-section">
      <h2>Test API Connection</h2>
      <form method="POST" action="?/testConnection" use:enhance={handleEnhance}>
        <button type="submit" class="button" disabled={loading}>
          {loading ? 'Testing...' : 'Test Connection'}
        </button>
      </form>
    </div>
    
    <div class="test-section">
      <h2>Test Single Wallet Generation</h2>
      <form method="POST" action="?/testSingleWallet" use:enhance={handleEnhance}>
        <div class="form-group">
          <label for="currency" class="form-label">Currency:</label>
          <select id="currency" name="currency" class="form-input" bind:value={selectedCurrency}>
            {#each currencies as currency}
              <option value={currency}>{currency}</option>
            {/each}
          </select>
        </div>
        <button type="submit" class="button" disabled={loading}>
          {loading ? 'Generating...' : `Generate ${selectedCurrency} Wallet`}
        </button>
      </form>
    </div>
    
    {#if form}
      <div class="result-section">
        <h3>Result:</h3>
        {#if form.success}
          <div class="message success">
            <strong>Success:</strong> {form.message}
            {#if form.result}
              <pre>{JSON.stringify(form.result, null, 2)}</pre>
            {/if}
          </div>
        {:else}
          <div class="message error">
            <strong>Error:</strong> {form.error}
            {#if form.currency}
              <p>Currency: {form.currency}</p>
            {/if}
          </div>
        {/if}
      </div>
    {/if}
  </div>
</div>

<style>
  .test-section {
    margin: 2rem 0;
    padding: 1rem;
    border: 1px solid #333;
    border-radius: 0.5rem;
  }
  
  .result-section {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #1a202c;
    border-radius: 0.5rem;
  }
  
  pre {
    background-color: #2d3748;
    padding: 1rem;
    border-radius: 0.25rem;
    overflow-x: auto;
    font-size: 0.875rem;
    margin-top: 0.5rem;
  }
  
  .message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
  }
  
  .message.success {
    background-color: #065f46;
    border: 1px solid #10b981;
    color: #d1fae5;
  }
  
  .message.error {
    background-color: #7f1d1d;
    border: 1px solid #ef4444;
    color: #fecaca;
  }
</style>
