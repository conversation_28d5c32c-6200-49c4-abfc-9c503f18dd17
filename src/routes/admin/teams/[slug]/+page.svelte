<script lang="ts">
    import type { PageData } from "./$types";
    import { enhance } from "$app/forms";
    import type { ActionResult } from "@sveltejs/kit";
    import { goto } from "$app/navigation";
    import { invalidateAll } from "$app/navigation";

    export let data: PageData;

    // Define the expected shape of the team data
    type Team = {
        id: string;
        internal_id?: string | null;
        balance: number;
        created_at: string;
        next_charge_at?: string | null;
        owner_id?: string | null;
        owner_internal_id?: string | null;
    };

    // Define the expected shape of the data returned from the server update action
    type UpdateActionData =
        | {
              success: boolean;
              data?: Team; // Optional: if the server returns the updated team
              error?: string; // Optional: if the server returns an error message
          }
        | undefined; // actionData can be undefined initially

    // Device type definition
    type Device = {
        internal_id: string;
        created_at: string;
        team_id: string;
        ip?: string;
        role?: string;
        nickname?: string;
        last_auth_at?: string;
        vpn_conf?: string;
        msg_conf?: string;
        phone_conf?: string;
    };

    // Wallet type definition
    type Wallet = {
        internal_id: number;
        created_at: string;
        team_id: string;
        currency: string;
        address: string;
    };

    // Statistics type definition
    type Statistics = {
        wallets: number;
        transactions: number;
        deposits: number;
        devices: number;
    };

    // Type assertion for data.team and initialize arrays
    let team: Team | null = data.team ? (data.team as Team) : null;
    let devices: Device[] = data.devices || [];
    let wallets: Wallet[] = data.wallets || [];
    let statistics: Statistics = data.statistics || { wallets: 0, transactions: 0, deposits: 0, devices: 0 };
    let showDeleteConfirmation = false;

    // Initialize form fields for editable properties
    let balance: number | undefined = team?.balance;

    let successMessage: string | null = null;
    let errorMessage: string | null = null;
    let isSubmitting = false; // To disable button during submission

    // Update local team variable when data.team changes
    $: team = data.team ? (data.team as Team) : null;

    // Update form fields when the local team variable changes
    $: if (team) {
        balance = team.balance;
        // Clear messages on team change
        successMessage = null;
        errorMessage = null;
    } else {
        // Reset fields if team data is no longer available
        balance = undefined;
    }

    // Enhance options for the form
    const enhanceOptions = ({
        formElement,
        formData,
        action,
        cancel,
        controller,
        submitter
    }: {
        formElement: HTMLFormElement;
        formData: FormData;
        action: URL;
        cancel: () => void;
        controller: AbortController;
        submitter: HTMLElement | null;
    }) => {
        // Pending logic runs here
        isSubmitting = true;
        successMessage = null;
        errorMessage = null;

        return async ({ result }: { result: ActionResult<UpdateActionData> }) => {
            // Type result with ActionResult and UpdateActionData
            isSubmitting = false;

            // Process result based on its type
            if (result.type === "success") {
                // Access action data from result.data
                const actionData = result.data as UpdateActionData; // Cast result.data to our expected type
                if (actionData?.success) {
                    successMessage = "Team updated successfully!";
                    // Redirect to the new team ID page if it changed
                    if (team?.id) {
                        goto(`/admin/teams/${team.id}`);
                    } else {
                        goto("/admin/teams");
                    }
                } else if (actionData?.error) {
                    errorMessage = actionData.error;
                } else {
                    // Handle cases where actionData is undefined or malformed on success type
                    errorMessage =
                        "An unexpected success response was received.";
                }
            } else if (result.type === "failure") {
                // Access action data from result.data
                const actionData = result.data as UpdateActionData; // Cast result.data to our expected type
                errorMessage =
                    actionData?.error || "Failed to update team.";
            } else if (result.type === "error") {
                // Handle server errors (e.g., 500 status)
                // result.error contains the actual Error object in this case
                errorMessage =
                    result.error?.message ||
                    `Server error with status ${result.status}`;
                console.error("Server error result:", result);
            } else if (result.type === "redirect") {
                // Handle redirects initiated by the server action
                // Clear any messages and invalidate all data before redirecting
                successMessage = null;
                errorMessage = null;
                // Invalidate all data to ensure fresh data is loaded on the target page
                invalidateAll().then(() => {
                    goto(result.location);
                });
            }
        };
    };

    // Function to format date without seconds
    function formatDate(dateString: string | undefined) {
        if (!dateString) return "-";
        try {
            const date = new Date(dateString);
            return date.toLocaleString(undefined, {
                year: "numeric",
                month: "numeric",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
            });
        } catch {
            return "Invalid date";
        }
    }

    // Function to get role badge class
    function getRoleBadgeClass(role: string | null | undefined) {
        if (!role) return 'role-badge';
        switch(role.toLowerCase()) {
            case 'admin':
                return 'role-badge role-admin';
            case 'user':
                return 'role-badge role-user';
            default:
                return 'role-badge';
        }
    }

    // Function to display time ago
    function timeAgo(dateString: string | undefined) {
        if (!dateString) return "-";
        try {
            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now.getTime() - date.getTime();
            const seconds = Math.floor(diffMs / 1000);
            let interval = Math.floor(seconds / 31536000);
            if (interval > 1) return interval + ' years ago';
            interval = Math.floor(seconds / 2592000);
            if (interval > 1) return interval + ' months ago';
            interval = Math.floor(seconds / 86400);
            if (interval > 1) return interval + ' days ago';
            interval = Math.floor(seconds / 3600);
            if (interval > 1) return interval + ' hours ago';
            interval = Math.floor(seconds / 60);
            if (interval > 1) return interval + ' minutes ago';
            return Math.floor(seconds) + ' seconds ago';
        } catch {
            return 'Unknown time';
        }
    }
</script>

<div class="admin-container">
    {#if team}
        <div class="admin-card">
            <h1 class="admin-title">{team.id} - Edit Team</h1>

            <hr style="margin-top: 0.5rem; border-color: #333;" />

            <div class="team-info-grid">
                <div class="team-info-item">
                    <strong>ID:</strong>
                    {team.id || "N/A"}
                    {#if team.internal_id}
                        <div style="font-size: 0.8em; margin-top: 0.2em;">
                            <strong>Internal ID:</strong>
                            {team.internal_id}
                        </div>
                    {/if}
                </div>

                <div class="team-info-item date-group">
                    <div>
                        <strong>Created At:</strong>
                        {formatDate(team.created_at)}
                    </div>
                    {#if team.next_charge_at}
                        <div>
                            <strong>Next Charge At:</strong>
                            {formatDate(team.next_charge_at)}
                        </div>
                    {/if}
                </div>

                {#if team.owner_id}
                    <div class="team-info-item">
                        <strong>Owner ID:</strong>
                        {team.owner_id}
                    </div>
                {/if}
                {#if team.owner_internal_id}
                    <div class="team-info-item">
                        <strong style="font-size: 0.9em;"
                            >Owner Internal ID:</strong
                        >
                        {team.owner_internal_id}
                    </div>
                {/if}
            </div>

            <hr
                style="margin-top: -0.5rem; margin-bottom: 1rem; border-color: #333;"
            />

            <form method="POST" action="?/update" use:enhance={enhanceOptions}>
                <div class="horizontal-layout">
                    <div class="form-group">
                        <label for="id" class="form-label">ID</label>
                        <input
                            id="id"
                            class="form-input"
                            type="text"
                            bind:value={team.id}
                            placeholder="Enter team ID"
                            name="id"
                            required
                        />
                    </div>
                </div>

                <div class="form-actions" style="margin-top: 1rem;">
                    <button on:click|preventDefault={()=>goto("/admin/teams")} class="button" disabled={isSubmitting}
                        >Cancel</button
                    >
                    <button type="submit" class="button" disabled={isSubmitting}
                        >Save Changes</button
                    >
                </div>
            </form>

            {#if successMessage}
                <div class="message success message-container">
                    {successMessage}
                </div>
            {/if}

            {#if errorMessage}
                <div class="message error message-container">
                    {errorMessage}
                </div>
            {/if}

            <!-- Devices Section -->
            <div class="section">
                <h2>Devices ({devices.length})</h2>
                {#if devices.length > 0}
                    <table class="client-table">
                        <style>
                            .role-badge {
                                display: inline-block;
                                padding: 4px 8px;
                                border-radius: 12px;
                                font-size: 0.8em;
                                font-weight: 600;
                                text-transform: capitalize;
                                background-color: #e2e8f0;
                                color: #4a5568;
                            }
                            
                            .role-admin {
                                background-color: #fef2f2;
                                color: #dc2626;
                            }
                            
                            .role-user {
                                background-color: #eff6ff;
                                color: #2563eb;
                            }
                            
                            .text-muted {
                                color: #a0aec0;
                            }
                            
                            .text-link {
                                color: #5b6eff;
                                text-decoration: none;
                            }
                            
                            .text-link:hover {
                                text-decoration: underline;
                            }
                        </style>
                        <thead>
                            <tr>
                                <th style="padding: 4px 4px;">IP</th>
                                <th style="padding: 4px 4px;">Nickname</th>
                                <th style="padding: 4px 4px;">Role</th>
                                <th style="padding: 4px 4px;">Last Active</th>
                                <th style="padding: 4px 4px;">Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each devices as device}
                                <tr>
                                    <td style="padding: 4px 4px; font-weight: bold; color: #ffd700;">{device.ip || '-'}</td>
                                    <td style="padding: 4px 4px;">{device.nickname || '-'}</td>
                                    <td style="padding: 4px 4px;">
                                        {#if device.role}
                                            <span class={getRoleBadgeClass(device.role)}>{device.role}</span>
                                        {:else}
                                            <span class="text-muted">-</span>
                                        {/if}
                                    </td>
                                    <td style="padding: 4px 4px;">{device.last_auth_at ? timeAgo(device.last_auth_at) : 'Never'}</td>
                                    <td style="padding: 4px 4px;">{timeAgo(device.created_at)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {:else}
                    <div class="empty-state">
                        <p>No devices found for this team.</p>
                    </div>
                {/if}
            </div>

            <!-- Wallets Section -->
            <div class="section">
                <h2>Wallets ({wallets.length})</h2>
                {#if wallets.length > 0}
                    <table class="client-table">
                        <thead>
                            <tr>
                                <th style="padding: 4px 4px;">Currency</th>
                                <th style="padding: 4px 4px;">Address</th>
                            </tr>
                        </thead>
                        <tbody>
                            {#each wallets as wallet}
                                <tr>
                                    <td style="padding: 4px 4px; font-weight: bold; color: #ffd700;">{wallet.currency}</td>
                                    <td style="padding: 4px 4px; font-family: monospace; font-size: 0.9em;">{wallet.address}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {:else}
                    <div class="empty-state">
                        <p>No wallets found for this team.</p>
                        <p style="font-size: 0.9em; margin-top: 0.5rem; color: #a0aec0;">
                            Wallets are automatically generated when a team is created.
                        </p>
                    </div>
                {/if}
            </div>

            <!-- Delete Team Section -->
            <div class="section danger-section">
                <h2>Danger Zone</h2>
                <div class="danger-content">
                    <div class="danger-info">
                        <h3>Delete Team</h3>
                        <p>Permanently delete this team and all associated data. This action cannot be undone.</p>

                        <div class="deletion-summary">
                            <h4>The following data will be deleted:</h4>
                            <ul>
                                <li>{statistics.devices} device{statistics.devices !== 1 ? 's' : ''}</li>
                                <li>{statistics.wallets} wallet{statistics.wallets !== 1 ? 's' : ''}</li>
                                <li>{statistics.transactions} transaction{statistics.transactions !== 1 ? 's' : ''}</li>
                                <li>{statistics.deposits} deposit{statistics.deposits !== 1 ? 's' : ''}</li>
                                <li>The team record itself</li>
                            </ul>
                        </div>
                    </div>

                    {#if !showDeleteConfirmation}
                        <button
                            class="button button-danger"
                            on:click={() => showDeleteConfirmation = true}
                        >
                            Delete Team
                        </button>
                    {:else}
                        <div class="delete-confirmation">
                            <p><strong>Are you sure?</strong> This will permanently delete team "{team?.id}" and all associated data.</p>
                            <div class="confirmation-buttons">
                                <button
                                    class="button button-secondary"
                                    on:click={() => showDeleteConfirmation = false}
                                >
                                    Cancel
                                </button>
                                <form method="POST" action="?/delete" style="display: inline;" use:enhance={enhanceOptions}>
                                    <button type="submit" class="button button-danger">
                                        Yes, Delete Team
                                    </button>
                                </form>
                            </div>
                        </div>
                    {/if}
                </div>
            </div>
        </div>
    {:else}
        <p class="empty-state">
            Loading team data or team not found...
        </p>
    {/if}
</div>

<style>
    .section {
        margin-top: 2rem;
    }

    .table-container {
        margin-top: 1rem;
        overflow-x: auto;
        border-radius: 0.5rem;
        border: 1px solid #2d3748;
    }

    .empty-state {
        padding: 1.5rem;
        background-color: #1a202c;
        border-radius: 0.5rem;
        text-align: center;
        color: #a0aec0;
        border: 1px solid #2d3748;
        margin-top: 1rem;
    }

    .danger-section {
        border: 1px solid #dc2626;
        border-radius: 0.5rem;
        background-color: #7f1d1d;
        padding: 1.5rem;
        margin-top: 3rem;
    }

    .danger-section h2 {
        color: #fecaca;
        margin-top: 0;
    }

    .danger-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .danger-info {
        flex: 1;
    }

    .danger-info h3 {
        color: #fecaca;
        margin-top: 0;
        margin-bottom: 0.5rem;
    }

    .danger-info p {
        color: #fca5a5;
        margin-bottom: 1rem;
    }

    .deletion-summary {
        background-color: #991b1b;
        padding: 1rem;
        border-radius: 0.25rem;
        border: 1px solid #dc2626;
    }

    .deletion-summary h4 {
        color: #fecaca;
        margin-top: 0;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .deletion-summary ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #fca5a5;
    }

    .deletion-summary li {
        margin-bottom: 0.25rem;
    }

    .button-danger {
        background-color: #dc2626;
        color: white;
        border: 1px solid #dc2626;
    }

    .button-danger:hover {
        background-color: #b91c1c;
        border-color: #b91c1c;
    }

    .delete-confirmation {
        text-align: center;
    }

    .delete-confirmation p {
        color: #fecaca;
        margin-bottom: 1rem;
        font-weight: 500;
    }

    .confirmation-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    @media (max-width: 768px) {
        .danger-content {
            flex-direction: column;
            align-items: stretch;
        }

        .confirmation-buttons {
            flex-direction: column;
        }
    }
</style>
