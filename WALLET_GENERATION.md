# Wallet Generation Feature

This document describes the automatic wallet generation feature implemented for team creation.

## Overview

When a new team is created, the system automatically generates 4 cryptocurrency wallets using the WestWallet API:

- **BTC** - Bitcoin
- **ETH** - Ethereum
- **USDT** - Tether USD
- **TRX** - TRON

*Note: The currency list can be updated in the `SUPPORTED_CURRENCIES` array in `walletService.js` based on WestWallet API support.*

## Implementation

### Database Schema

The `wallets` table has been added to store wallet information:

```sql
CREATE TABLE wallets (
    internal_id INT8 PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    team_internal_id UUID,
    team_id TEXT,
    currency TEXT,
    address TEXT NOT NULL,
    dest_tag TEXT,
    FOREIGN KEY (team_internal_id) REFERENCES teams(internal_id),
    FOREIGN KEY (team_id) REFERENCES teams(id)
);
```

### Components

1. **Wallet Service** (`src/lib/server/walletService.js`)
   - Handles wallet generation using the npm-installed `westwallet-api` package
   - Manages database operations for wallets
   - Provides functions for retrieving team wallets

2. **Team Creation Integration**
   - Updated `src/routes/admin/teams/new/+page.server.js`
   - Updated `src/routes/admin/teams/+page.server.js`
   - Automatically generates wallets after successful team creation

3. **Admin Interface**
   - Updated team detail page to display wallets
   - Shows wallet addresses, currencies, and creation dates
   - Added comprehensive team deletion with cascade cleanup

4. **Team Management Service** (`src/lib/server/teamService.js`)
   - Centralized team deletion logic
   - Cascading deletion of all associated data
   - Statistics tracking for deletion confirmation

### Dependencies

The implementation uses the npm-installed `westwallet-api` package. Make sure you have the following dependencies installed:

```bash
npm install westwallet-api axios
```

### Environment Configuration

Add the following environment variables to your `.env` file:

```env
# WestWallet API Configuration
WESTWALLET_API_KEY=your_westwallet_api_key_here
WESTWALLET_SECRET_KEY=your_westwallet_secret_key_here
WESTWALLET_BASE_URL=https://api.westwallet.io
```

## Usage

### Automatic Generation

Wallets are automatically generated when:
- Creating a new team via `/admin/teams/new/`
- Creating a new team via the teams list page

### Manual Testing

You can test the wallet service using the test file:

```javascript
import { testWalletGeneration, testWalletRetrievalEmpty } from './src/lib/server/walletService.test.js';

// Test wallet retrieval for non-existent team
await testWalletRetrievalEmpty();

// Test actual wallet generation (requires valid API credentials)
await testWalletGeneration();
```

### Viewing Wallets

1. Navigate to `/admin/teams/`
2. Click on a team ID to view team details
3. Scroll down to the "Wallets" section
4. View generated wallet addresses for each currency

## Error Handling

- If wallet generation fails, team creation still succeeds
- Errors are logged to the console for debugging
- Failed wallet generations don't prevent team creation
- Empty wallet lists are handled gracefully in the UI

## API Integration

The system uses the WestWallet API's `/address/generate` endpoint:

```javascript
const addressData = await client.generateAddress(currency, ipnUrl, label);
```

Each wallet is labeled with the format: `team_{teamId}_{currency}`

## Security Considerations

- API credentials are stored in environment variables
- Wallet addresses are stored in the database for quick access
- No private keys are stored or handled by the application
- All wallet operations are logged for audit purposes

## Future Enhancements

Potential improvements:
- Manual wallet regeneration for specific currencies
- Wallet balance checking integration
- Payment notification handling via IPN URLs
- Wallet address validation
- Support for additional cryptocurrencies
